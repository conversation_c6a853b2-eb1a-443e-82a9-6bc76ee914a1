#!/usr/bin/env python3
"""
Simple WhatsApp Bot using pywhatkit
This is the easiest approach for sending messages and downloading Spotify tracks
"""

import pywhatkit as kit
import subprocess
import os
import uuid
import json
from datetime import datetime, timedelta

class WhatsAppSpotifyBot:
    def __init__(self):
        self.downloads_dir = "downloads"
        self.ensure_downloads_dir()

    def ensure_downloads_dir(self):
        """Create downloads directory if it doesn't exist"""
        if not os.path.exists(self.downloads_dir):
            os.makedirs(self.downloads_dir)

    def send_message(self, phone_number, message):
        """Send a WhatsApp message using pywhatkit"""
        try:
            print(f"Sending message to {phone_number}: {message}")
            kit.sendwhatmsg_instantly(phone_number, message, wait_time=15, tab_close=True)
            print(f"✅ Message sent successfully!")
            return True
        except Exception as e:
            print(f"❌ Error sending message: {e}")
            return False

    def send_scheduled_message(self, phone_number, message, hour, minute):
        """Send a scheduled WhatsApp message"""
        try:
            print(f"Scheduling message to {phone_number} at {hour}:{minute}")
            kit.sendwhatmsg(phone_number, message, hour, minute, wait_time=15, tab_close=True)
            print(f"✅ Scheduled message sent!")
            return True
        except Exception as e:
            print(f"❌ Error sending scheduled message: {e}")
            return False

    def download_spotify_content(self, spotify_url_or_query):
        """Download Spotify track/playlist using spotdl"""
        try:
            # Create unique directory for this download
            request_id = str(uuid.uuid4())
            output_path = os.path.join(self.downloads_dir, request_id)
            os.makedirs(output_path, exist_ok=True)

            # Clean URL if it's a Spotify link
            if spotify_url_or_query.startswith('https://open.spotify.com/'):
                cleaned_input = spotify_url_or_query.split('?')[0]
            else:
                # It's a search query
                cleaned_input = f'"{spotify_url_or_query}"'

            print(f"Downloading: {cleaned_input}")

            # Run spotdl command
            command = ["spotdl", "--output", output_path, cleaned_input]
            result = subprocess.run(command, capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                # Find downloaded files
                files = []
                for file in os.listdir(output_path):
                    if file.endswith('.mp3'):
                        files.append(os.path.join(output_path, file))

                if files:
                    return {
                        "success": True,
                        "files": files,
                        "temp_dir": output_path,
                        "count": len(files)
                    }

            print(f"spotdl stderr: {result.stderr}")
            return {
                "success": False,
                "files": [],
                "temp_dir": output_path,
                "error": result.stderr
            }

        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "files": [],
                "temp_dir": output_path,
                "error": "Download timeout (5 minutes)"
            }
        except Exception as e:
            print(f"Error downloading: {e}")
            return {
                "success": False,
                "files": [],
                "temp_dir": "",
                "error": str(e)
            }

    def process_spotify_request(self, phone_number, spotify_input):
        """Process Spotify download request and send updates"""
        # Send processing message
        if spotify_input.startswith('https://open.spotify.com/'):
            if '/playlist/' in spotify_input:
                self.send_message(phone_number, "🎵 Processing your Spotify playlist...")
            else:
                self.send_message(phone_number, "🎵 Processing your Spotify track...")
        else:
            self.send_message(phone_number, f"🔍 Searching for: {spotify_input}")

        # Download the content
        result = self.download_spotify_content(spotify_input)

        if result["success"] and result["files"]:
            if result["count"] > 1:
                self.send_message(phone_number, f"✅ Downloaded {result['count']} songs successfully!")
            else:
                filename = os.path.basename(result["files"][0])
                self.send_message(phone_number, f"✅ Downloaded: {filename}")

            # List all downloaded files
            for file_path in result["files"]:
                filename = os.path.basename(file_path)
                self.send_message(phone_number, f"🎵 {filename}")

            return result["files"]
        else:
            error_msg = result.get("error", "Unknown error")
            self.send_message(phone_number, f"❌ Failed to download: {error_msg}")
            return []

    def cleanup_downloads(self, temp_dir):
        """Clean up downloaded files"""
        try:
            if os.path.exists(temp_dir):
                for file in os.listdir(temp_dir):
                    os.remove(os.path.join(temp_dir, file))
                os.rmdir(temp_dir)
                print(f"Cleaned up: {temp_dir}")
        except Exception as e:
            print(f"Cleanup error: {e}")

def main():
    """Main function with interactive menu"""
    bot = WhatsAppSpotifyBot()

    print("🤖 WhatsApp Spotify Bot")
    print("=" * 30)

    while True:
        print("\nOptions:")
        print("1. Send Spotify download")
        print("2. Send custom message")
        print("3. Schedule message")
        print("4. Exit")

        choice = input("\nEnter your choice (1-4): ").strip()

        if choice == "1":
            phone = input("Enter phone number (with country code, e.g., +1234567890): ").strip()
            spotify_input = input("Enter Spotify URL or song name: ").strip()

            if phone and spotify_input:
                files = bot.process_spotify_request(phone, spotify_input)
                if files:
                    cleanup = input("Clean up downloaded files? (y/n): ").strip().lower()
                    if cleanup == 'y':
                        # Get temp dir from first file path
                        temp_dir = os.path.dirname(files[0])
                        bot.cleanup_downloads(temp_dir)

        elif choice == "2":
            phone = input("Enter phone number (with country code): ").strip()
            message = input("Enter message: ").strip()

            if phone and message:
                bot.send_message(phone, message)

        elif choice == "3":
            phone = input("Enter phone number (with country code): ").strip()
            message = input("Enter message: ").strip()
            hour = int(input("Enter hour (24-hour format): "))
            minute = int(input("Enter minute: "))

            if phone and message:
                bot.send_scheduled_message(phone, message, hour, minute)

        elif choice == "4":
            print("Goodbye!")
            break

        else:
            print("Invalid choice. Please try again.")

if __name__ == "__main__":
    main()
