#!/usr/bin/env python3
"""
Simple WhatsApp Bot using pywhatkit
This is the easiest approach but has limitations
"""

import pywhatkit as kit
import time
import schedule
import subprocess
import os
import json
from datetime import datetime

def send_whatsapp_message(phone_number, message):
    """Send a WhatsApp message using pywhatkit"""
    try:
        # Send message immediately (you can also schedule for later)
        kit.sendwhatmsg_instantly(phone_number, message, wait_time=10, tab_close=True)
        print(f"Message sent to {phone_number}")
        return True
    except Exception as e:
        print(f"Error sending message: {e}")
        return False

def download_spotify_track(spotify_url):
    """Download Spotify track using spotdl"""
    try:
        # Create downloads directory
        downloads_dir = "downloads"
        if not os.path.exists(downloads_dir):
            os.makedirs(downloads_dir)
        
        # Run spotdl command
        command = ["spotdl", "--output", downloads_dir, spotify_url]
        result = subprocess.run(command, capture_output=True, text=True)
        
        if result.returncode == 0:
            # Find downloaded files
            files = [f for f in os.listdir(downloads_dir) if f.endswith('.mp3')]
            if files:
                return os.path.join(downloads_dir, files[-1])  # Return latest file
        
        return None
    except Exception as e:
        print(f"Error downloading: {e}")
        return None

def process_spotify_request(phone_number, spotify_url):
    """Process Spotify download request"""
    # Send processing message
    send_whatsapp_message(phone_number, "🎵 Processing your Spotify link...")
    
    # Download the track
    file_path = download_spotify_track(spotify_url)
    
    if file_path:
        # Send success message
        filename = os.path.basename(file_path)
        send_whatsapp_message(phone_number, f"✅ Downloaded: {filename}")
        
        # You can also send the file (requires additional setup)
        # send_whatsapp_file(phone_number, file_path)
    else:
        send_whatsapp_message(phone_number, "❌ Failed to download the track")

# Example usage
if __name__ == "__main__":
    # Example: Send a message
    phone_number = "+1234567890"  # Replace with actual number
    spotify_url = "https://open.spotify.com/track/example"
    
    # Process request
    process_spotify_request(phone_number, spotify_url)
