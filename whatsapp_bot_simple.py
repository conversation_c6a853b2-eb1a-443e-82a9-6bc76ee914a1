#!/usr/bin/env python3
"""
WhatsApp Spotify Bot - Python equivalent of the Node.js bot
Automatically listens for messages and responds with Spotify downloads
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import time
import os
import subprocess
import uuid
import shutil
import re

class WhatsAppSpotifyBot:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.downloads_dir = "downloads"
        self.processed_messages = set()
        self.ensure_downloads_dir()
        self.setup_driver()

    def ensure_downloads_dir(self):
        """Create downloads directory if it doesn't exist"""
        if not os.path.exists(self.downloads_dir):
            os.makedirs(self.downloads_dir)

    def setup_driver(self):
        """Setup Chrome driver for WhatsApp Web"""
        chrome_options = Options()
        chrome_options.add_argument("--user-data-dir=./whatsapp_session")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        self.wait = WebDriverWait(self.driver, 20)

    def login(self):
        """Login to WhatsApp Web"""
        print("🔄 Opening WhatsApp Web...")
        self.driver.get("https://web.whatsapp.com")

        print("📱 Please scan the QR code with your phone...")
        try:
            # Wait for successful login (chat list appears)
            self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='chat-list']")))
            print("✅ Successfully logged in!")
            time.sleep(3)  # Wait for full load
            return True
        except TimeoutException:
            print("❌ Login timeout. Please try again.")
            return False

    def send_message(self, message):
        """Send message to current chat"""
        try:
            # Find message input box
            message_box = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "[data-testid='conversation-compose-box-input']"))
            )

            # Clear and send message
            message_box.clear()
            message_box.send_keys(message)
            message_box.send_keys(Keys.ENTER)

            print(f"📤 Sent: {message}")
            time.sleep(1)
            return True

        except Exception as e:
            print(f"❌ Failed to send message: {e}")
            return False

    def download_spotify_content(self, spotify_url_or_query):
        """Download Spotify content using spotdl"""
        try:
            # Create unique directory for this download
            request_id = str(uuid.uuid4())
            output_path = os.path.join(self.downloads_dir, request_id)
            os.makedirs(output_path, exist_ok=True)

            # Clean URL if it's a Spotify link
            if spotify_url_or_query.startswith('https://open.spotify.com/'):
                cleaned_input = spotify_url_or_query.split('?')[0]
            else:
                # It's a search query
                cleaned_input = f'"{spotify_url_or_query}"'

            print(f"🎵 Downloading: {cleaned_input}")

            # Run spotdl command
            command = ["spotdl", "--output", output_path, cleaned_input]
            result = subprocess.run(command, capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                # Find downloaded files
                files = []
                for file in os.listdir(output_path):
                    if file.endswith('.mp3'):
                        files.append(os.path.join(output_path, file))

                return {
                    "files": files,
                    "temp_dir": output_path
                }

            print(f"❌ spotdl error: {result.stderr}")
            return {
                "files": [],
                "temp_dir": output_path
            }

        except subprocess.TimeoutExpired:
            print("❌ Download timeout (5 minutes)")
            return {
                "files": [],
                "temp_dir": output_path
            }
        except Exception as e:
            print(f"❌ Download error: {e}")
            return {
                "files": [],
                "temp_dir": ""
            }

    def cleanup_downloads(self, temp_dir):
        """Clean up downloaded files"""
        try:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
                print(f"🧹 Cleaned up: {temp_dir}")
        except Exception as e:
            print(f"❌ Cleanup error: {e}")

    def process_spotify_message(self, message_text):
        """Process Spotify-related message"""
        try:
            if 'spotify.com/track' in message_text or 'spotify.com/playlist' in message_text:
                self.send_message('🎵 Processing your Spotify link...')

                # Extract Spotify URL
                spotify_url = re.search(r'https://open\.spotify\.com/[^\s]+', message_text)
                if spotify_url:
                    result = self.download_spotify_content(spotify_url.group())
                    song_paths = result["files"]
                    temp_dir = result["temp_dir"]

                    if len(song_paths) == 0:
                        self.send_message('❌ Could not download any songs. The playlist might be empty or there was an error.')
                        self.cleanup_downloads(temp_dir)
                        return

                    if len(song_paths) > 1:
                        self.send_message(f'✅ Playlist processed! Found {len(song_paths)} songs. Sending them one by one...')
                    else:
                        self.send_message('✅ MP3 ready! Sending...')

                    # Send file names (actual file sending would require additional implementation)
                    for song_path in song_paths:
                        filename = os.path.basename(song_path).replace('.mp3', '')
                        self.send_message(f'🎵 Now sending: *{filename}*')
                        time.sleep(2)

                    # Clean up after processing
                    self.cleanup_downloads(temp_dir)

            elif len(message_text) > 3 and not message_text.startswith('/') and not message_text.startswith('http'):
                # Treat as song search query
                self.send_message('🔍 Searching for your song...')

                result = self.download_spotify_content(message_text)
                song_paths = result["files"]
                temp_dir = result["temp_dir"]

                if len(song_paths) == 0:
                    self.send_message('❌ Could not find any songs matching your search. Try being more specific.')
                    self.cleanup_downloads(temp_dir)
                    return

                self.send_message('✅ Song found! Sending...')

                for song_path in song_paths:
                    filename = os.path.basename(song_path).replace('.mp3', '')
                    self.send_message(f'🎵 Now sending: *{filename}*')
                    time.sleep(2)

                # Clean up after processing
                self.cleanup_downloads(temp_dir)

        except Exception as e:
            print(f"❌ Error processing message: {e}")
            self.send_message('❌ An error occurred while processing your request.')

    def get_new_messages(self):
        """Get new messages from current chat"""
        try:
            # Get all message containers
            messages = self.driver.find_elements(By.CSS_SELECTOR, "[data-testid='msg-container']")

            new_messages = []
            for msg in messages[-5:]:  # Check last 5 messages
                try:
                    # Check if it's an incoming message (not sent by us)
                    if "message-in" in msg.get_attribute("class"):
                        # Get message text
                        text_elements = msg.find_elements(By.CSS_SELECTOR, ".selectable-text")
                        if text_elements:
                            message_text = text_elements[0].text

                            # Create unique message ID
                            msg_id = f"{message_text[:50]}_{int(time.time())}"

                            if msg_id not in self.processed_messages:
                                new_messages.append(message_text)
                                self.processed_messages.add(msg_id)

                                # Keep processed messages list manageable
                                if len(self.processed_messages) > 100:
                                    self.processed_messages = set(list(self.processed_messages)[-50:])

                except Exception as e:
                    continue

            return new_messages

        except Exception as e:
            print(f"❌ Error getting messages: {e}")
            return []

    def run(self):
        """Main bot loop - equivalent to the Node.js bot"""
        if not self.login():
            return

        print("🤖 Bot is ready!")
        print("💡 The bot will automatically respond to Spotify links and song searches in any chat.")
        print("📱 Open any chat in WhatsApp Web and send a Spotify link or song name.")
        print("🛑 Press Ctrl+C to stop the bot.")

        try:
            while True:
                # Check for new messages in the currently open chat
                new_messages = self.get_new_messages()

                for message in new_messages:
                    print(f"📨 New message: {message}")
                    self.process_spotify_message(message)

                time.sleep(2)  # Check every 2 seconds

        except KeyboardInterrupt:
            print("\n🛑 Bot stopped by user")
        except Exception as e:
            print(f"❌ Bot error: {e}")
        finally:
            self.driver.quit()

if __name__ == "__main__":
    bot = WhatsAppSpotifyBot()
    bot.run()
