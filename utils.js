const { exec } = require('child_process');
const path = require('path');

function processSpotifyLink(url) {
  return new Promise((resolve, reject) => {
    const pythonScriptPath = path.resolve(__dirname, 'downloader.py');
    const pythonPath = 'python3';
    const cmd = `"${pythonPath}" "${pythonScriptPath}" "${url}"`;
    exec(cmd, (err, stdout, stderr) => {
      if (err) {
        console.error(`exec error: ${err}`);
        console.error(`python stderr: ${stderr}`);
        return reject(stderr);
      }
      
      const output = stdout.trim();
      try {
        const result = JSON.parse(output);
        if (result && typeof result === 'object' && Array.isArray(result.files)) {
          resolve(result);
        } else {
          reject('Python script did not return a valid result object.');
        }
      } catch (parseError) {
        console.error('Failed to parse python output:', output);
        reject('Could not parse result from python script.');
      }
    });
  });
}

module.exports = {
  processSpotifyLink
};
