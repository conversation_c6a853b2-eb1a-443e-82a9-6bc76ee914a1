const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

function processSpotifyLink(url) {
  return new Promise((resolve, reject) => {
    // Create unique download directory
    const baseOutputPath = path.join(__dirname, 'downloads');
    const requestId = uuidv4();
    const outputPath = path.join(baseOutputPath, requestId);

    // Ensure downloads directory exists
    if (!fs.existsSync(baseOutputPath)) {
      fs.mkdirSync(baseOutputPath, { recursive: true });
    }
    fs.mkdirSync(outputPath, { recursive: true });

    // Clean URL (remove query parameters)
    const cleanedUrl = url.split('?')[0];

    // Build spotdl command
    const cmd = `spotdl --output "${outputPath}" "${cleanedUrl}"`;

    exec(cmd, (err, stdout, stderr) => {
      if (err) {
        console.error(`exec error: ${err}`);
        console.error(`spotdl stderr: ${stderr}`);
        // Return empty result instead of rejecting
        return resolve({ files: [], temp_dir: outputPath });
      }

      try {
        // Parse downloaded files from spotdl output
        const outputLines = stdout.trim().split('\n');
        const downloadedFiles = [];

        for (const line of outputLines) {
          if (line.includes('Downloaded')) {
            try {
              // Extract file path from spotdl output
              const match = line.match(/Downloaded ".*?" to "(.*?)"/);
              if (match && match[1]) {
                downloadedFiles.push(match[1]);
              }
            } catch (parseErr) {
              console.error('Error parsing download line:', line);
            }
          }
        }

        // If no files found in output, check directory for any .mp3 files
        if (downloadedFiles.length === 0) {
          try {
            const files = fs.readdirSync(outputPath);
            const mp3Files = files
              .filter(file => file.endsWith('.mp3'))
              .map(file => path.join(outputPath, file));

            resolve({ files: mp3Files, temp_dir: outputPath });
          } catch (readErr) {
            console.error('Error reading output directory:', readErr);
            resolve({ files: [], temp_dir: outputPath });
          }
        } else {
          resolve({ files: downloadedFiles, temp_dir: outputPath });
        }

      } catch (parseError) {
        console.error('Failed to parse spotdl output:', stdout);
        console.error('Parse error:', parseError);
        resolve({ files: [], temp_dir: outputPath });
      }
    });
  });
}

module.exports = {
  processSpotifyLink
};
