#!/usr/bin/env python3
"""
Advanced WhatsApp Bot using Selenium + WhatsApp Web
This provides more control and can read incoming messages
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
import time
import os
import subprocess
import json
import uuid
from urllib.parse import quote

class WhatsAppBot:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.setup_driver()
    
    def setup_driver(self):
        """Setup Chrome driver for WhatsApp Web"""
        chrome_options = Options()
        chrome_options.add_argument("--user-data-dir=./whatsapp_session")  # Save session
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        # chrome_options.add_argument("--headless")  # Uncomment for headless mode
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.wait = WebDriverWait(self.driver, 20)
        
    def login(self):
        """Login to WhatsApp Web"""
        print("Opening WhatsApp Web...")
        self.driver.get("https://web.whatsapp.com")
        
        print("Please scan the QR code with your phone...")
        # Wait for login (QR code scan)
        try:
            self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='chat-list']")))
            print("✅ Successfully logged in!")
            return True
        except Exception as e:
            print(f"❌ Login failed: {e}")
            return False
    
    def send_message(self, contact_name, message):
        """Send message to a contact"""
        try:
            # Search for contact
            search_box = self.wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "[data-testid='chat-list-search']")))
            search_box.clear()
            search_box.send_keys(contact_name)
            time.sleep(2)
            
            # Click on contact
            contact = self.wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, f"[title='{contact_name}']")))
            contact.click()
            
            # Send message
            message_box = self.wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "[data-testid='conversation-compose-box-input']")))
            message_box.send_keys(message)
            message_box.send_keys(Keys.ENTER)
            
            print(f"✅ Message sent to {contact_name}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to send message: {e}")
            return False
    
    def get_new_messages(self):
        """Get new unread messages"""
        try:
            # Find unread chats
            unread_chats = self.driver.find_elements(By.CSS_SELECTOR, "[data-testid='cell-frame-container'] [data-testid='icon-unread-count']")
            
            messages = []
            for chat in unread_chats[:5]:  # Process max 5 unread chats
                try:
                    # Click on chat
                    chat_container = chat.find_element(By.XPATH, "./ancestor::div[@data-testid='cell-frame-container']")
                    contact_name = chat_container.find_element(By.CSS_SELECTOR, "[data-testid='cell-frame-title']").text
                    chat_container.click()
                    time.sleep(1)
                    
                    # Get messages
                    message_elements = self.driver.find_elements(By.CSS_SELECTOR, "[data-testid='msg-container']")
                    
                    for msg_elem in message_elements[-3:]:  # Get last 3 messages
                        try:
                            text_elem = msg_elem.find_element(By.CSS_SELECTOR, ".selectable-text")
                            message_text = text_elem.text
                            
                            # Check if it's an incoming message (not sent by us)
                            if "message-in" in msg_elem.get_attribute("class"):
                                messages.append({
                                    "contact": contact_name,
                                    "message": message_text,
                                    "timestamp": time.time()
                                })
                        except:
                            continue
                            
                except Exception as e:
                    print(f"Error processing chat: {e}")
                    continue
            
            return messages
            
        except Exception as e:
            print(f"Error getting messages: {e}")
            return []
    
    def download_spotify_track(self, spotify_url):
        """Download Spotify track using spotdl"""
        try:
            downloads_dir = "downloads"
            request_id = str(uuid.uuid4())
            output_path = os.path.join(downloads_dir, request_id)
            os.makedirs(output_path, exist_ok=True)
            
            # Clean URL
            cleaned_url = spotify_url.split('?')[0]
            
            # Run spotdl
            command = ["spotdl", "--output", output_path, cleaned_url]
            result = subprocess.run(command, capture_output=True, text=True)
            
            if result.returncode == 0:
                # Find downloaded files
                files = [os.path.join(output_path, f) for f in os.listdir(output_path) if f.endswith('.mp3')]
                return {"files": files, "temp_dir": output_path}
            
            return {"files": [], "temp_dir": output_path}
            
        except Exception as e:
            print(f"Error downloading: {e}")
            return {"files": [], "temp_dir": ""}
    
    def process_message(self, contact, message):
        """Process incoming message"""
        print(f"Processing message from {contact}: {message}")
        
        if "spotify.com" in message:
            self.send_message(contact, "🎵 Processing your Spotify link...")
            
            result = self.download_spotify_track(message)
            
            if result["files"]:
                filename = os.path.basename(result["files"][0])
                self.send_message(contact, f"✅ Downloaded: {filename}")
                # Note: File sending requires additional implementation
            else:
                self.send_message(contact, "❌ Failed to download the track")
        
        elif len(message) > 3 and not message.startswith('/'):
            # Treat as song search
            self.send_message(contact, "🔍 Searching for your song...")
            
            result = self.download_spotify_track(f'"{message}"')
            
            if result["files"]:
                filename = os.path.basename(result["files"][0])
                self.send_message(contact, f"✅ Found and downloaded: {filename}")
            else:
                self.send_message(contact, "❌ Could not find the song")
    
    def run(self):
        """Main bot loop"""
        if not self.login():
            return
        
        print("🤖 Bot is running...")
        processed_messages = set()
        
        try:
            while True:
                messages = self.get_new_messages()
                
                for msg in messages:
                    msg_id = f"{msg['contact']}_{msg['timestamp']}_{msg['message'][:20]}"
                    
                    if msg_id not in processed_messages:
                        self.process_message(msg['contact'], msg['message'])
                        processed_messages.add(msg_id)
                
                time.sleep(5)  # Check every 5 seconds
                
        except KeyboardInterrupt:
            print("Bot stopped by user")
        except Exception as e:
            print(f"Bot error: {e}")
        finally:
            self.driver.quit()

if __name__ == "__main__":
    bot = WhatsAppBot()
    bot.run()
